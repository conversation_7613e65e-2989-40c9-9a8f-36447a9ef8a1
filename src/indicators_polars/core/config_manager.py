"""
Configuration manager for indicators and processing parameters
"""

import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime, date
import pytz


logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """Database configuration"""
    host: str
    port: int
    username: str
    password: str
    database: str
    table_prefix: str = "sensibull_index_"
    connection_pool_size: int = 10
    query_timeout: int = 30


@dataclass
class MarketConfig:
    """Market configuration"""
    timezone: str = "Asia/Kolkata"
    start_time: str = "09:15"
    end_time: str = "15:30"
    symbols: List[str] = None
    current_week: bool = True
    next_week: bool = True
    additional_weeks: int = 0
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = ["NIFTY", "BANKNIFTY", "MIDCPNIFTY", "FINNIFTY"]


@dataclass
class ProcessingConfig:
    """Processing configuration"""
    mode: str = "live"  # "live" or "historical"
    workers: int = 4
    batch_size: int = 10000
    max_processing_time_seconds: int = 50
    polling_interval_seconds: int = 60
    delta_only: bool = True
    start_date: Optional[str] = None
    end_date: Optional[str] = None


@dataclass
class TechnicalIndicatorConfig:
    """Technical indicator configuration"""
    rsi_enabled: bool = True
    rsi_period: int = 14
    rsi_columns: List[str] = None
    
    macd_enabled: bool = True
    macd_fast_period: int = 12
    macd_slow_period: int = 26
    macd_signal_period: int = 9
    macd_columns: List[str] = None
    
    sma_enabled: bool = True
    sma_periods: List[int] = None
    sma_columns: List[str] = None
    
    ema_enabled: bool = True
    ema_periods: List[int] = None
    ema_columns: List[str] = None
    
    def __post_init__(self):
        if self.rsi_columns is None:
            self.rsi_columns = ["last_price", "current_price"]
        if self.macd_columns is None:
            self.macd_columns = ["last_price"]
        if self.sma_periods is None:
            self.sma_periods = [5, 10, 20, 50]
        if self.sma_columns is None:
            self.sma_columns = ["last_price", "oi", "volume"]
        if self.ema_periods is None:
            self.ema_periods = [5, 10, 20, 50]
        if self.ema_columns is None:
            self.ema_columns = ["last_price", "oi", "volume"]


@dataclass
class CustomIndicatorConfig:
    """Custom indicator configuration"""
    rankings_enabled: bool = True
    ranking_columns: List[str] = None
    ranking_window_size: int = 100
    
    intrinsic_value_enabled: bool = True
    patterns_enabled: bool = True
    consecutive_periods: int = 3
    
    trading_signals_enabled: bool = True
    buy_signals: List[str] = None
    sell_signals: List[str] = None
    
    # Thresholds for trading signals
    thresholds: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.ranking_columns is None:
            self.ranking_columns = ["oi", "gamma", "delta", "theta", "vega", "iv", "volume"]
        if self.buy_signals is None:
            self.buy_signals = ["BUY1", "BUY2", "BUY3", "BUY4", "BUY5"]
        if self.sell_signals is None:
            self.sell_signals = ["SELL1", "SELL2", "SELL3", "SELL4", "SELL5"]
        if self.thresholds is None:
            self.thresholds = {
                'candle_dip': -2.0,
                'candle_rise': 2.0,
                'int_value_rise': [0.8, 0.6, 0.4],
                'int_value_drop': [-0.8, -0.6, -0.4],
                'premium_rise': [0.8, 0.6, 0.4],
                'premium_drop': [-0.8, -0.6, -0.4],
                'gamma_rise': [0.8, 0.6, 0.4],
                'del_vol_rise': [0.8, 0.6, 0.4],
                'iv_double_rise': [0.8, 0.6],
                'iv_triple_rise': [0.8, 0.6, 0.4],
                'iv_drop': [-0.8, -0.6, -0.4],
                'delta_rise': [0.8, 0.6, 0.4],
                'delta_drop': [-0.8, -0.6, -0.4],
                'theta_rise': [0.8, 0.6, 0.4],
                'theta_drop': [-0.8, -0.6, -0.4],
                'oi_rise': [0.8, 0.6, 0.4],
                'vega_cool': [-0.8, -0.6, -0.4]
            }


@dataclass
class OutputConfig:
    """Output configuration"""
    table_name: str = "indicators_output"
    create_table: bool = True
    batch_insert_size: int = 1000


@dataclass
class MonitoringConfig:
    """Monitoring configuration"""
    enabled: bool = True
    latency_threshold_seconds: int = 45
    error_threshold_count: int = 5


class ConfigManager:
    """Manage configuration for the indicators system"""
    
    def __init__(self, config_dict: Dict[str, Any]):
        self.raw_config = config_dict
        self._parse_config()
    
    def _parse_config(self):
        """Parse configuration dictionary into typed objects"""
        # Database configuration
        db_config = self.raw_config.get('database', {}).get('clickhouse', {})
        self.database = DatabaseConfig(
            host=db_config['host'],
            port=db_config['port'],
            username=db_config['username'],
            password=db_config['password'],
            database=db_config['database'],
            table_prefix=db_config.get('table_prefix', 'sensibull_index_'),
            connection_pool_size=db_config.get('connection_pool_size', 10),
            query_timeout=db_config.get('query_timeout', 30)
        )
        
        # Market configuration
        market_config = self.raw_config.get('market', {})
        expiry_filter = market_config.get('expiry_filter', {})
        self.market = MarketConfig(
            timezone=market_config.get('timezone', 'Asia/Kolkata'),
            start_time=market_config.get('start_time', '09:15'),
            end_time=market_config.get('end_time', '15:30'),
            symbols=market_config.get('symbols', ["NIFTY", "BANKNIFTY", "MIDCPNIFTY", "FINNIFTY"]),
            current_week=expiry_filter.get('current_week', True),
            next_week=expiry_filter.get('next_week', True),
            additional_weeks=expiry_filter.get('additional_weeks', 0)
        )
        
        # Processing configuration
        proc_config = self.raw_config.get('processing', {})
        historical_config = proc_config.get('historical', {})
        live_config = proc_config.get('live', {})
        
        self.processing = ProcessingConfig(
            mode=proc_config.get('mode', 'live'),
            workers=proc_config.get('workers', 4),
            batch_size=proc_config.get('batch_size', 10000),
            max_processing_time_seconds=proc_config.get('max_processing_time_seconds', 50),
            polling_interval_seconds=live_config.get('polling_interval_seconds', 60),
            delta_only=live_config.get('delta_only', True),
            start_date=historical_config.get('start_date'),
            end_date=historical_config.get('end_date')
        )
        
        # Technical indicators configuration
        indicators_config = self.raw_config.get('indicators', {})
        tech_config = indicators_config.get('technical', {})
        
        rsi_config = tech_config.get('rsi', {})
        macd_config = tech_config.get('macd', {})
        sma_config = tech_config.get('sma', {})
        ema_config = tech_config.get('ema', {})
        
        self.technical_indicators = TechnicalIndicatorConfig(
            rsi_enabled=rsi_config.get('enabled', True),
            rsi_period=rsi_config.get('period', 14),
            rsi_columns=rsi_config.get('columns', ["last_price", "current_price"]),
            macd_enabled=macd_config.get('enabled', True),
            macd_fast_period=macd_config.get('fast_period', 12),
            macd_slow_period=macd_config.get('slow_period', 26),
            macd_signal_period=macd_config.get('signal_period', 9),
            macd_columns=macd_config.get('columns', ["last_price"]),
            sma_enabled=sma_config.get('enabled', True),
            sma_periods=sma_config.get('periods', [5, 10, 20, 50]),
            sma_columns=sma_config.get('columns', ["last_price", "oi", "volume"]),
            ema_enabled=ema_config.get('enabled', True),
            ema_periods=ema_config.get('periods', [5, 10, 20, 50]),
            ema_columns=ema_config.get('columns', ["last_price", "oi", "volume"])
        )
        
        # Custom indicators configuration
        custom_config = indicators_config.get('custom', {})
        rankings_config = custom_config.get('rankings', {})
        intrinsic_config = custom_config.get('intrinsic_value', {})
        patterns_config = custom_config.get('patterns', {})
        signals_config = custom_config.get('trading_signals', {})
        
        self.custom_indicators = CustomIndicatorConfig(
            rankings_enabled=rankings_config.get('enabled', True),
            ranking_columns=rankings_config.get('columns', ["oi", "gamma", "delta", "theta", "vega", "iv", "volume"]),
            ranking_window_size=rankings_config.get('window_size', 100),
            intrinsic_value_enabled=intrinsic_config.get('enabled', True),
            patterns_enabled=patterns_config.get('enabled', True),
            consecutive_periods=patterns_config.get('consecutive_periods', 3),
            trading_signals_enabled=signals_config.get('enabled', True),
            buy_signals=signals_config.get('buy_signals', ["BUY1", "BUY2", "BUY3", "BUY4", "BUY5"]),
            sell_signals=signals_config.get('sell_signals', ["SELL1", "SELL2", "SELL3", "SELL4", "SELL5"]),
            thresholds=signals_config.get('thresholds', {})
        )
        
        # Output configuration
        output_config = self.raw_config.get('output', {}).get('clickhouse', {})
        self.output = OutputConfig(
            table_name=output_config.get('table_name', 'indicators_output'),
            create_table=output_config.get('create_table', True),
            batch_insert_size=output_config.get('batch_insert_size', 1000)
        )
        
        # Monitoring configuration
        monitoring_config = self.raw_config.get('output', {}).get('monitoring', {})
        self.monitoring = MonitoringConfig(
            enabled=monitoring_config.get('enabled', True),
            latency_threshold_seconds=monitoring_config.get('latency_threshold_seconds', 45),
            error_threshold_count=monitoring_config.get('error_threshold_count', 5)
        )
    
    def get_timezone(self) -> pytz.BaseTzInfo:
        """Get timezone object"""
        return pytz.timezone(self.market.timezone)
    
    def is_market_time(self, current_time: datetime) -> bool:
        """Check if current time is within market hours"""
        tz = self.get_timezone()
        if current_time.tzinfo is None:
            current_time = tz.localize(current_time)
        else:
            current_time = current_time.astimezone(tz)
        
        start_time = datetime.strptime(self.market.start_time, '%H:%M').time()
        end_time = datetime.strptime(self.market.end_time, '%H:%M').time()
        
        current_time_only = current_time.time()
        return start_time <= current_time_only <= end_time
    
    def get_processing_date(self) -> date:
        """Get the date to process based on mode"""
        if self.processing.mode == 'historical' and self.processing.start_date:
            return datetime.strptime(self.processing.start_date, '%Y-%m-%d').date()
        else:
            # For live mode, use current date
            tz = self.get_timezone()
            return datetime.now(tz).date()
    
    def validate(self) -> bool:
        """Validate configuration"""
        try:
            # Validate processing mode
            if self.processing.mode not in ['live', 'historical']:
                raise ValueError(f"Invalid processing mode: {self.processing.mode}")
            
            # Validate historical mode requirements
            if self.processing.mode == 'historical' and not self.processing.start_date:
                raise ValueError("Historical mode requires start_date")
            
            # Validate market symbols
            if not self.market.symbols:
                raise ValueError("Market symbols cannot be empty")
            
            # Validate time format
            datetime.strptime(self.market.start_time, '%H:%M')
            datetime.strptime(self.market.end_time, '%H:%M')
            
            logger.info("Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False
