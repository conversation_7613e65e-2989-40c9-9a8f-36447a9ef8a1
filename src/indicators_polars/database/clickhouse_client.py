"""
ClickHouse client with connection pooling and error handling
"""

import logging
import polars as pl
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, date
import clickhouse_connect
from clickhouse_connect.driver import Client
import pytz
from contextlib import contextmanager


logger = logging.getLogger(__name__)


class ClickHouseClient:
    """ClickHouse client with connection pooling and polars integration"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('database', {}).get('clickhouse', {})
        self.market_config = config.get('market', {})
        logger.info(f"Market config: {self.market_config}")
        self.client: Optional[Client] = None
        self.timezone = pytz.timezone(self.market_config.get('timezone', 'Asia/Kolkata'))
        self._connect()
    
    def _connect(self):
        """Establish connection to ClickHouse"""
        try:
            self.client = clickhouse_connect.get_client(
                host=self.config['host'],
                port=self.config['port'],
                username=self.config['username'],
                password=self.config['password'],
                database=self.config['database'],
                connect_timeout=self.config.get('query_timeout', 30),
                send_receive_timeout=self.config.get('query_timeout', 30)
            )
            
            # Test connection
            self.client.ping()
            logger.info(f"Connected to ClickHouse at {self.config['host']}:{self.config['port']}")

        except Exception as e:
            logger.error(f"Failed to connect to ClickHouse: {e}")
            raise

    def test_connection(self) -> bool:
        """Test the ClickHouse connection"""
        try:
            result = self.client.query("SELECT 1")
            return len(result.result_rows) > 0
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False
    
    @contextmanager
    def get_client(self):
        """Context manager for getting client with automatic reconnection"""
        try:
            if not self.client:
                self._connect()
            yield self.client
        except Exception as e:
            logger.warning(f"ClickHouse connection issue, attempting reconnect: {e}")
            try:
                self._connect()
                yield self.client
            except Exception as reconnect_error:
                logger.error(f"Failed to reconnect to ClickHouse: {reconnect_error}")
                raise
    
    def get_table_name(self, target_date: date) -> str:
        """Get table name for a specific date"""
        table_prefix = self.config.get('table_prefix', 'sensibull_index_')
        return f"{table_prefix}{target_date.strftime('%d_%m_%Y')}"
    
    def table_exists(self, table_name: str) -> bool:
        """Check if table exists"""
        try:
            with self.get_client() as client:
                result = client.query(
                    "SELECT 1 FROM system.tables WHERE database = %(database)s AND name = %(table)s",
                    parameters={
                        'database': self.config['database'],
                        'table': table_name
                    }
                )
                return len(result.result_rows) > 0
        except Exception as e:
            logger.error(f"Error checking table existence {table_name}: {e}")
            return False
    
    def get_data_for_date(
        self, 
        target_date: date, 
        symbols: Optional[List[str]] = None,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        delta_only: bool = False,
        last_timestamp: Optional[datetime] = None
    ) -> pl.DataFrame:
        """
        Get data for a specific date with optional filtering
        
        Args:
            target_date: Date to fetch data for
            symbols: List of symbols to filter (None for all)
            start_time: Start time in HH:MM format
            end_time: End time in HH:MM format  
            delta_only: If True, only fetch data newer than last_timestamp
            last_timestamp: Last processed timestamp for delta fetching
        """
        table_name = self.get_table_name(target_date)
        logger.info(f"Printing market_config: {self.market_config}")
        
        if not self.table_exists(table_name):
            logger.warning(f"Table {table_name} does not exist")
            return pl.DataFrame()
        
        # Build query conditions
        conditions = []
        parameters = {}
        
        # Date filtering
        if start_time and end_time:
            start_datetime = datetime.combine(target_date, datetime.strptime(start_time, '%H:%M').time())
            end_datetime = datetime.combine(target_date, datetime.strptime(end_time, '%H:%M').time())
            
            # Convert to timezone-aware datetimes
            start_datetime = self.timezone.localize(start_datetime)
            end_datetime = self.timezone.localize(end_datetime)
            
            conditions.append("timestamp >= %(start_time)s AND timestamp <= %(end_time)s")
            parameters['start_time'] = start_datetime
            parameters['end_time'] = end_datetime
        
        # Symbol filtering
        if symbols:
            symbol_placeholders = ','.join([f"'{symbol}'" for symbol in symbols])
            conditions.append(f"symbol IN ({symbol_placeholders})")

        # Expiry type filtering (from config)
        logger.info(f"Market config expiry_types: {self.market_config.get('expiry_types')}")
        if self.market_config.get('expiry_types'):
            expiry_types = self.market_config['expiry_types']
            expiry_type_placeholders = ','.join([f"'{et}'" for et in expiry_types])
            conditions.append(f"expiry_type IN ({expiry_type_placeholders})")
            logger.info(f"Condition added for expiry types: {expiry_type_placeholders}")

        # Strike filtering (from config)
        logger.info(f"Market config strikes: {self.market_config.get('strikes')}")
        if self.market_config.get('strikes'):
            strikes = self.market_config['strikes']
            strike_placeholders = ','.join([str(strike) for strike in strikes])
            conditions.append(f"strike IN ({strike_placeholders})")
            logger.info(f"Condition added for strikes: {strike_placeholders}")

        # Delta filtering for live mode
        if delta_only and last_timestamp:
            conditions.append("timestamp > %(last_timestamp)s")
            parameters['last_timestamp'] = last_timestamp
        
        # Top 2 expiries per symbol filtering
        logger.info(f"Market config top_expiries_per_symbol: {self.market_config.get('top_expiries_per_symbol')}")
        top_expiries_filter = self.market_config.get('top_expiries_per_symbol', 2)
        if top_expiries_filter and top_expiries_filter > 0:
            # We'll handle this in post-processing since ClickHouse window functions are complex
            # For now, we'll fetch all data and filter in Polars
            pass
        
        # Build final query
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        
        query = f"""
        SELECT *
        FROM {table_name}
        WHERE {where_clause}
        ORDER BY timestamp, symbol, expiry_date, expiry_type, strike
        """
        logger.info(f"Final query: {query}")
        
        try:
            with self.get_client() as client:
                logger.info(f"Executing query on {table_name} with {len(conditions)} conditions")
                result = client.query(query, parameters=parameters)
                
                if not result.result_rows:
                    logger.info(f"No data found for {target_date}")
                    return pl.DataFrame()
                
                # Convert to Polars DataFrame
                df = pl.DataFrame(result.result_rows, schema=result.column_names, orient="row")
                logger.info(f"Fetched {len(df)} rows from {table_name}")

                # Apply top expiries per symbol filtering
                top_expiries_filter = self.market_config.get('top_expiries_per_symbol', 2)
                if top_expiries_filter and top_expiries_filter > 0 and not df.is_empty():
                    df = self._filter_top_expiries_per_symbol(df, top_expiries_filter)
                    logger.info(f"After expiry filtering: {len(df)} rows")

                return df
                
        except Exception as e:
            logger.error(f"Error fetching data from {table_name}: {e}")
            raise

    def _filter_top_expiries_per_symbol(self, df: pl.DataFrame, top_n: int = 2) -> pl.DataFrame:
        """
        Filter to keep only top N expiries per symbol

        Args:
            df: Input DataFrame
            top_n: Number of top expiries to keep per symbol
        """
        if df.is_empty() or 'symbol' not in df.columns or 'expiry_date' not in df.columns:
            return df

        try:
            # Get unique expiries per symbol and rank them
            expiry_ranking = (
                df
                .select(['symbol', 'expiry_date'])
                .unique()
                .sort(['symbol', 'expiry_date'])
                .with_columns([
                    pl.col('expiry_date').rank('ordinal').over('symbol').alias('expiry_rank')
                ])
                .filter(pl.col('expiry_rank') <= top_n)
                .select(['symbol', 'expiry_date'])
            )

            # Log expiries for each symbol
            expiry_summary = (
                expiry_ranking
                .group_by('symbol')
                .agg([
                    pl.col('expiry_date').sort().alias('expiries')
                ])
            )

            for row in expiry_summary.iter_rows(named=True):
                symbol = row['symbol']
                expiries = row['expiries']
                logger.info(f"Symbol {symbol}: Top {top_n} expiries: {expiries}")

            # Join back to original data to filter
            filtered_df = df.join(
                expiry_ranking,
                on=['symbol', 'expiry_date'],
                how='inner'
            )

            logger.info(f"Filtered to top {top_n} expiries per symbol: {len(filtered_df)} rows")
            return filtered_df

        except Exception as e:
            logger.error(f"Error filtering top expiries: {e}")
            return df

    def get_latest_timestamp(self, target_date: date, symbols: Optional[List[str]] = None) -> Optional[datetime]:
        """Get the latest timestamp from the table"""
        table_name = self.get_table_name(target_date)
        
        if not self.table_exists(table_name):
            return None
        
        conditions = []
        if symbols:
            symbol_placeholders = ','.join([f"'{symbol}'" for symbol in symbols])
            conditions.append(f"symbol IN ({symbol_placeholders})")
        
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        
        query = f"""
        SELECT MAX(timestamp) as latest_timestamp
        FROM {table_name}
        WHERE {where_clause}
        """
        
        try:
            with self.get_client() as client:
                result = client.query(query)
                if result.result_rows and result.result_rows[0][0]:
                    return result.result_rows[0][0]
                return None
        except Exception as e:
            logger.error(f"Error getting latest timestamp from {table_name}: {e}")
            return None

    def create_output_table(self, table_name: str) -> bool:
        """Create output table for indicators if it doesn't exist"""
        create_table_sql = f"""
        CREATE TABLE IF NOT EXISTS {table_name}
        (
            symbol Enum8('UNKNOWN' = 0, 'NIFTY' = 1, 'BANKNIFTY' = 2, 'FINNIFTY' = 3, 'MIDCPNIFTY' = 4),
            timestamp DateTime,
            expiry_date Date,
            strike Float32,
            expiry_type Enum8('UNKNOWN' = 0, 'CE' = 1, 'PE' = 2, 'FUTURE' = 3),

            -- Original data columns
            current_price Nullable(Float32),
            last_price Nullable(Float32),
            oi Nullable(Float32),
            volume Nullable(Float32),
            delta_volume Nullable(Float32),
            delta Nullable(Float32),
            gamma Nullable(Float32),
            theta Nullable(Float32),
            vega Nullable(Float32),
            iv Nullable(Float32),

            -- Technical indicators
            rsi_last_price Nullable(Float32),
            rsi_current_price Nullable(Float32),
            macd_last_price Nullable(Float32),
            macd_signal_last_price Nullable(Float32),
            macd_histogram_last_price Nullable(Float32),
            sma_5_last_price Nullable(Float32),
            sma_10_last_price Nullable(Float32),
            sma_20_last_price Nullable(Float32),
            sma_50_last_price Nullable(Float32),
            ema_5_last_price Nullable(Float32),
            ema_10_last_price Nullable(Float32),
            ema_20_last_price Nullable(Float32),
            ema_50_last_price Nullable(Float32),

            -- Ranking indicators
            oi_rank Nullable(Float32),
            gamma_rank Nullable(Float32),
            delta_rank Nullable(Float32),
            theta_rank Nullable(Float32),
            vega_rank Nullable(Float32),
            iv_rank Nullable(Float32),
            delta_volume_rank Nullable(Float32),

            -- Custom indicators
            intrinsic_value Nullable(Float32),
            intrinsic_value_rank Nullable(Float32),
            premium_rank Nullable(Float32),

            -- Signal counts (moved to avoid duplicates)
            standard_buy_count Nullable(UInt32),
            special_buy_count Nullable(UInt32),
            sell_count Nullable(UInt32),

            -- Special conditions
            iv_vega_condition String,
            gamma_condition String,

            -- Metadata
            calculation_timestamp DateTime DEFAULT now(),

            -- NEW derived indicators from DF
            sma_5_oi Nullable(Float32),
            sma_10_oi Nullable(Float32),
            sma_20_oi Nullable(Float32),
            sma_50_oi Nullable(Float32),
            sma_5_volume Nullable(Float32),
            sma_10_volume Nullable(Float32),
            sma_20_volume Nullable(Float32),
            sma_50_volume Nullable(Float32),
            ema_5_oi Nullable(Float32),
            ema_10_oi Nullable(Float32),
            ema_20_oi Nullable(Float32),
            ema_50_oi Nullable(Float32),
            ema_5_volume Nullable(Float32),
            ema_10_volume Nullable(Float32),
            ema_20_volume Nullable(Float32),
            ema_50_volume Nullable(Float32),
            price_change_pct Nullable(Float32),
            oi_change Nullable(Float32),
            consecutive_price_drop UInt32,
            consecutive_oi_build UInt32,
            candle_dip UInt32,
            candle_rise UInt32,
            candle_dip_signal UInt32,
            candle_rise_signal UInt32,
            ints_value_rise UInt32,
            ints_value_drop UInt32,
            premium_rise UInt32,
            premium_drop UInt32,
            gamma_rise UInt32,
            del_vol_rise UInt32,
            iv_double_rise UInt32,
            iv_triple_rise UInt32,
            iv_drop UInt32,
            delta_rise UInt32,
            delta_drop UInt32,
            theta_rise UInt32,
            theta_drop UInt32,
            oi_rise UInt32,
            vega_cool UInt32,
            gamma_move String,
            buy_signal_1 String,
            buy_signal_2 String,
            buy_signal_3 String,
            buy_signal_4 String,
            buy_signal_5 String,
            sell_signal_1 String,
            sell_signal_2 String,
            sell_signal_3 String,
            sell_signal_4 String,
            sell_signal_5 String,
            buy_with_iv_vega String,
            buy_with_gamma String,
            sell_with_iv String,
            gamma_present String,

            INDEX idx_symbol symbol TYPE minmax GRANULARITY 1,
            INDEX idx_timestamp timestamp TYPE minmax GRANULARITY 1,
            INDEX idx_expiry expiry_date TYPE minmax GRANULARITY 1
        )
        ENGINE = MergeTree
        ORDER BY (timestamp, symbol, expiry_date, expiry_type, strike)
        SETTINGS index_granularity = 8192;
        """

        try:
            with self.get_client() as client:
                client.command(create_table_sql)
                logger.info(f"Output table {table_name} created/verified")
                return True
        except Exception as e:
            logger.error(f"Error creating output table {table_name}: {e}")
            return False

    def insert_indicators_data(self, df: pl.DataFrame, table_name: str, batch_size: int = 1000) -> bool:
        """Insert indicators data into ClickHouse table"""
        if df.is_empty():
            logger.info("No data to insert")
            return True

        try:
            logger.debug(f"Available columns before insertion: {df.columns}")
            # get the columns from the table and compare with df.columns
            with self.get_client() as client:
                result = client.query(f"DESCRIBE TABLE {table_name}")
                table_columns = [row[0] for row in result.result_rows]
                logger.debug(f"Table columns: {table_columns}")
                # Print lenght of columns in DF and TABLE
                logger.info(f"Length of df columns: {len(df.columns)}")
                logger.info(f"Length of table columns: {len(table_columns)}")
                
                #find difference in columns between the df and table_columns
                missing_columns = [col for col in table_columns if col not in df.columns]
                if missing_columns:
                    logger.warning(f"Missing columns in dataframe: {missing_columns}")
                # find missing columns in table_columns from df
                extra_columns = [col for col in df.columns if col not in table_columns]
                if extra_columns:
                    logger.warning(f"Extra columns in dataframe: {extra_columns}")    


            # Handle non-finite values in Polars DataFrame first
            df_clean = df.with_columns([
                pl.col(col).fill_null(0).fill_nan(0) for col in df.columns if df[col].dtype in [pl.Float32, pl.Float64]
            ])

            # Handle NULL values in string columns
            string_columns = [col for col in df_clean.columns if df_clean[col].dtype == pl.Utf8]
            if string_columns:
                df_clean = df_clean.with_columns([
                    pl.col(col).fill_null("") for col in string_columns
                ])

            # Convert to pandas for ClickHouse insertion
            pandas_df = df_clean.to_pandas()

            # Replace any remaining inf values and NaN in object columns
            pandas_df = pandas_df.replace([float('inf'), float('-inf')], 0)

            # Fill any remaining NaN values in string columns
            string_cols = pandas_df.select_dtypes(include=['object']).columns
            for col in string_cols:
                pandas_df[col] = pandas_df[col].fillna("")

            with self.get_client() as client:
                try:
                    total = len(pandas_df)
                    logger.info(f"Inserting {total} rows into {table_name} in batches of {batch_size}")

                    # Use insert_df method which handles data types better
                    for i in range(0, total, batch_size):
                        batch_df = pandas_df.iloc[i:i + batch_size]
                        client.insert_df(table_name, batch_df)
                        logger.info(f"Inserted batch {i // batch_size + 1}, rows: {len(batch_df)}")

                except Exception as e:
                    logger.error(f"Error inserting batch: {e}")
                    raise
                    
                logger.info(f"Successfully inserted {total} rows into {table_name}")
                return True

        except Exception as e:
            logger.error(f"Error inserting data into {table_name}: {e}")
            return False
