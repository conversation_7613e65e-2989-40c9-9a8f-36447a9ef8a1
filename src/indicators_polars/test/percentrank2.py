import polars as pl

# Input data
values = [
0.0033,0.0036,0.0036,0.0037,0.0037,0.0038,0.0037,0.0037
]

df = pl.DataFrame({"Value": values})

# Progressive rank & percent rank
ranks = []
ranks_min = []
ranks_ordinal = []
ranks_max = []
percent_ranks = []

for i in range(df.height):
    # Take the slice up to row i
    subset = df[: i + 1]
    # Rank values in this slice
    ranked = subset.with_columns(
        subset["Value"].rank("average").alias("Rank")
    )
    ranked_min = subset.with_columns(
        subset["Value"].rank("min").alias("Rank_min")
    )
    ranked_ordinal = subset.with_columns(
        subset["Value"].rank("ordinal").alias("Rank_ordinal")
    )
    ranked_max = subset.with_columns(
        subset["Value"].rank("max").alias("Rank_max")
    )
    # Get the rank of the last row in the slice
    current_rank = ranked[-1, "Rank"]
    current_rank_min = ranked_min[-1, "Rank_min"]
    current_rank_ordinal = ranked_ordinal[-1, "Rank_ordinal"]
    current_rank_max = ranked_max[-1, "Rank_max"]
    ranks.append(current_rank)
    ranks_min.append(current_rank_min)
    ranks_ordinal.append(current_rank_ordinal)
    ranks_max.append(current_rank_max)
    # Percent rank
    if i == 0:
        percent_ranks.append(0.0)
    else:
        percent_ranks.append((current_rank - 1) / i)

# Add results to df
df = df.with_columns([
    pl.Series("Rank", ranks),
    pl.Series("Rank_min", ranks_min),
    pl.Series("Rank_max", ranks_max),
    pl.Series("Rank_ordinal", ranks_ordinal),
    pl.Series("Percent_Rank", percent_ranks)
])
df2= df.to_pandas()
print(df2)
